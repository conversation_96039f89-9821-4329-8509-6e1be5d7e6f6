// import ccxt from 'ccxt'
import type { ExchangeInfo, TradingPair, TickerData, ExchangeVolumeData } from '@/types/exchange'

// Mock data for demonstration
const MOCK_EXCHANGES = [
  'binance', 'coinbase', 'kraken', 'bitfinex', 'huobi', 'okx', 'kucoin',
  'bybit', 'gate', 'mexc', 'bitget', 'crypto.com', 'gemini', 'bitstamp'
]

const MOCK_SYMBOLS = [
  'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT', 'XRP/USDT',
  'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT', 'LINK/USDT', 'UNI/USDT',
  'LTC/USDT', 'BCH/USDT', 'ATOM/USDT', 'FIL/USDT', 'TRX/USDT', 'ETC/USDT'
]

class ExchangeService {
  private exchangeInfoCache: ExchangeInfo[] = []
  private cacheExpiry: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.initializeMockData()
  }

  private initializeMockData() {
    // Initialize with mock data for demonstration
    console.log(`Initializing ${MOCK_EXCHANGES.length} mock exchanges`)
  }

  async getAllExchanges(): Promise<ExchangeInfo[]> {
    if (this.exchangeInfoCache.length > 0 && Date.now() < this.cacheExpiry) {
      return this.exchangeInfoCache
    }

    const exchangeInfos: ExchangeInfo[] = MOCK_EXCHANGES.map(id => ({
      id,
      name: this.getExchangeName(id),
      countries: ['US'],
      urls: {
        logo: `https://logo.clearbit.com/${id}.com`,
        www: `https://${id}.com`,
        api: `https://api.${id}.com`
      },
      has: {
        fetchTicker: true,
        fetchTickers: true,
        fetchOrderBook: true,
        fetchTrades: true,
        fetchOHLCV: true
      },
      rateLimit: 1000,
      certified: Math.random() > 0.5,
      pro: Math.random() > 0.7,
      sandbox: false,
      status: 'ok'
    }))

    this.exchangeInfoCache = exchangeInfos
    this.cacheExpiry = Date.now() + this.CACHE_DURATION

    return exchangeInfos
  }

  private getExchangeName(id: string): string {
    const names: Record<string, string> = {
      'binance': 'Binance',
      'coinbase': 'Coinbase Pro',
      'kraken': 'Kraken',
      'bitfinex': 'Bitfinex',
      'huobi': 'Huobi Global',
      'okx': 'OKX',
      'kucoin': 'KuCoin',
      'bybit': 'Bybit',
      'gate': 'Gate.io',
      'mexc': 'MEXC',
      'bitget': 'Bitget',
      'crypto.com': 'Crypto.com',
      'gemini': 'Gemini',
      'bitstamp': 'Bitstamp'
    }
    return names[id] || id.charAt(0).toUpperCase() + id.slice(1)
  }

  async getExchangeTickers(exchangeId: string): Promise<TickerData[]> {
    if (!MOCK_EXCHANGES.includes(exchangeId)) {
      throw new Error(`Exchange ${exchangeId} not found`)
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))

    const tickerData: TickerData[] = MOCK_SYMBOLS.map(symbol => {
      const basePrice = this.getBasePrice(symbol)
      const volume = Math.random() * 10000000 + 100000 // Random volume between 100K and 10M
      const change = (Math.random() - 0.5) * 20 // Random change between -10% and +10%
      const last = basePrice * (1 + change / 100)

      return {
        symbol,
        timestamp: Date.now(),
        datetime: new Date().toISOString(),
        high: last * (1 + Math.random() * 0.05),
        low: last * (1 - Math.random() * 0.05),
        bid: last * 0.999,
        bidVolume: Math.random() * 1000,
        ask: last * 1.001,
        askVolume: Math.random() * 1000,
        vwap: last,
        open: basePrice,
        close: last,
        last,
        previousClose: basePrice,
        change: last - basePrice,
        percentage: change,
        average: (basePrice + last) / 2,
        baseVolume: volume / last,
        quoteVolume: volume,
        info: {},
        exchangeId,
        volumeUSDT: volume
      }
    })

    return tickerData.sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  }

  private getBasePrice(symbol: string): number {
    const prices: Record<string, number> = {
      'BTC/USDT': 45000,
      'ETH/USDT': 2500,
      'BNB/USDT': 300,
      'ADA/USDT': 0.5,
      'SOL/USDT': 100,
      'XRP/USDT': 0.6,
      'DOT/USDT': 7,
      'DOGE/USDT': 0.08,
      'AVAX/USDT': 35,
      'MATIC/USDT': 0.9,
      'LINK/USDT': 15,
      'UNI/USDT': 6,
      'LTC/USDT': 70,
      'BCH/USDT': 250,
      'ATOM/USDT': 12,
      'FIL/USDT': 5,
      'TRX/USDT': 0.1,
      'ETC/USDT': 20
    }
    return prices[symbol] || 1
  }

  async getExchangeVolume(exchangeId: string): Promise<ExchangeVolumeData> {
    try {
      const tickers = await this.getExchangeTickers(exchangeId)
      const totalVolumeUSDT = tickers.reduce((sum, ticker) => sum + (ticker.volumeUSDT || 0), 0)

      return {
        exchangeId,
        exchangeName: this.getExchangeName(exchangeId),
        totalVolumeUSDT,
        tickerCount: tickers.length,
        status: 'ok',
        lastUpdated: Date.now()
      }
    } catch (error) {
      console.error(`Failed to get volume for ${exchangeId}:`, error)
      return {
        exchangeId,
        exchangeName: this.getExchangeName(exchangeId),
        totalVolumeUSDT: 0,
        tickerCount: 0,
        status: 'error',
        lastUpdated: Date.now()
      }
    }
  }

  async getAllExchangeVolumes(): Promise<ExchangeVolumeData[]> {
    const volumes: ExchangeVolumeData[] = []

    for (const exchangeId of MOCK_EXCHANGES) {
      try {
        const volume = await this.getExchangeVolume(exchangeId)
        volumes.push(volume)
        // Add small delay to simulate real API calls
        await new Promise(resolve => setTimeout(resolve, 200))
      } catch (error) {
        console.warn(`Failed to get volume for ${exchangeId}:`, error)
      }
    }

    return volumes.sort((a, b) => b.totalVolumeUSDT - a.totalVolumeUSDT)
  }

  async getAllMarketTickers(): Promise<TickerData[]> {
    const allTickers: TickerData[] = []

    for (const exchangeId of MOCK_EXCHANGES) {
      try {
        const tickers = await this.getExchangeTickers(exchangeId)
        allTickers.push(...tickers)
        // Add small delay to simulate real API calls
        await new Promise(resolve => setTimeout(resolve, 200))
      } catch (error) {
        console.warn(`Failed to get tickers for ${exchangeId}:`, error)
      }
    }

    return allTickers.sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  }
}

export const exchangeService = new ExchangeService()
export default ExchangeService
